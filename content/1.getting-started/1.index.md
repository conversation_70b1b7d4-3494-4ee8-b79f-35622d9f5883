---
title: Introduction
description: Welcome to Nuxt UI Pro documentation template.
navigation:
  icon: i-lucide-house
---

This template is a ready-to-use documentation template made with [Nuxt UI Pro](https://ui.nuxt.com/pro), a collection of premium components built on top of [Nuxt UI](https://ui.nuxt.com) to create beautiful & responsive Nuxt applications in minutes.

There are already many websites based on this template:

::card-group
  :::card
  ---
  icon: i-simple-icons-nuxtdotjs
  target: _blank
  title: Nuxt
  to: https://nuxt.com
  ---
  The Nuxt website
  :::

  :::card
  ---
  icon: i-simple-icons-nuxtdotjs
  target: _blank
  title: Nuxt UI
  to: https://ui.nuxt.com
  ---
  The documentation of `@nuxt/ui` and `@nuxt/ui-pro`
  :::

  :::card
  ---
  icon: i-simple-icons-nuxtdotjs
  target: _blank
  title: Nuxt Image
  to: https://image.nuxt.com
  ---
  The documentation of `@nuxt/image`
  :::

  :::card
  ---
  icon: i-simple-icons-nuxtdotjs
  target: _blank
  title: Nuxt Content
  to: https://content.nuxt.com
  ---
  The documentation of `@nuxt/content`
  :::

  :::card
  ---
  icon: i-simple-icons-nuxtdotjs
  target: _blank
  title: Nuxt Devtools
  to: https://devtools.nuxt.com
  ---
  The documentation of `@nuxt/devtools`
  :::

  :::card
  ---
  icon: i-simple-icons-nuxtdotjs
  target: _blank
  title: Nuxt Hub
  to: https://hub.nuxt.com
  ---
  The best place to manage your projects, environments and variables.
  :::
::

## Key Features

This template includes a range of features designed to streamline documentation management:

- **Powered by** [**Nuxt 3**](https://nuxt.com): Utilizes the latest Nuxt framework for optimal performance.
- **Built with** [**Nuxt UI**](https://ui.nuxt.com) **and** [**Nuxt UI Pro**](https://ui.nuxt.com/pro): Integrates a comprehensive suite of UI components.
- [**MDC Syntax**](https://content.nuxt.com/usage/markdown) **via** [**Nuxt Content**](https://content.nuxt.com): Supports Markdown with component integration for dynamic content.
- [**Nuxt Studio**](https://content.nuxt.com/docs/studio) **Compatible**: Offers integration with Nuxt Studio for content editing.
- **Auto-generated Sidebar Navigation**: Automatically generates navigation from content structure.
- **Full-Text Search**: Includes built-in search functionality for content discovery.
- **Optimized Typography**: Features refined typography for enhanced readability.
- **Dark Mode**: Offers dark mode support for user preference.
- **Extensive Functionality**: Explore the template to fully appreciate its capabilities.
