---
title: Usage
description: Learn how to write and customize your documentation.
navigation:
  icon: i-lucide-sliders
---

This is only a basic example of what you can achieve with [Nuxt UI Pro](https://ui.nuxt.com/pro/guide), you can tweak it to match your needs. The template uses several Nuxt modules underneath like [`@nuxt/content`](https://content.nuxt.com) for the content and [`nuxt-og-image`](https://nuxtseo.com/og-image/getting-started/installation) for social previews.

::tip
---
target: _blank
to: https://ui.nuxt.com/getting-started/installation/pro/nuxt
---
Learn more on how to take the most out of Nuxt UI Pro!
::

## Writing content

You can just start writing `.md` or `.yml` files in the [`content/`](https://content.nuxt.com/usage/content-directory) directory to have your pages updated. The navigation will be automatically generated in the left aside and in the mobile menu. You will also be able to go through your content with full-text search.

## App Configuration

In addition to `@nuxt/ui-pro` configuration through the `app.config.ts`, this template lets you customize the `Header`, `Footer` and the `Table of contents` components.

### Header

```ts [app.config.ts]
export default defineAppConfig({
  header: {
    title: '',
    to: '/',
    // Logo configuration
    logo: {
      alt: '',
      // Light mode
      light: '',
      // Dark mode
      dark: ''
    },
    // Show or hide the search bar
    search: true,
    // Show or hide the color mode button
    colorMode: true,
    // Customize links
    links: [{
      'icon': 'i-simple-icons-github',
      'to': 'https://github.com/nuxt-ui-pro/docs',
      'target': '_blank',
      'aria-label': 'GitHub'
    }]
  },
})
```

### Footer

```ts [app.config.ts]
export default defineAppConfig({
  footer: {
    // Update bottom left credits
    credits: `Copyright © ${new Date().getFullYear()}`,
    // Show or hide the color mode button
    colorMode: false,
    // Customize links
    links: [{
      'icon': 'i-simple-icons-nuxtdotjs',
      'to': 'https://nuxt.com',
      'target': '_blank',
      'aria-label': 'Nuxt Website'
    }, {
      'icon': 'i-simple-icons-discord',
      'to': 'https://discord.com/invite/ps2h6QT',
      'target': '_blank',
      'aria-label': 'Nuxt UI on Discord'
    }, {
      'icon': 'i-simple-icons-x',
      'to': 'https://x.com/nuxt_js',
      'target': '_blank',
      'aria-label': 'Nuxt on X'
    }, {
      'icon': 'i-simple-icons-github',
      'to': 'https://github.com/nuxt/ui',
      'target': '_blank',
      'aria-label': 'Nuxt UI on GitHub'
    }]
  },
})
```

### Table of contents

```ts [app.config.ts]
export default defineAppConfig({
  toc: {
    // Title of the main table of contents
    title: 'Table of Contents',
    // Customize links
    bottom: {
      // Title of the bottom table of contents
      title: 'Community',
      // URL of your repository content folder
      edit: 'https://github.com/nuxt-ui-pro/docs/edit/main/content',
      links: [{
        icon: 'i-lucide-star',
        label: 'Star on GitHub',
        to: 'https://github.com/nuxt/ui',
        target: '_blank'
      }, {
        icon: 'i-lucide-book-open',
        label: 'Nuxt UI Pro docs',
        to: 'https://ui.nuxt.com/getting-started/installation/pro/nuxt',
        target: '_blank'
      }, {
        icon: 'i-simple-icons-nuxtdotjs',
        label: 'Purchase a license',
        to: 'https://ui.nuxt.com/pro/purchase',
        target: '_blank'
      }]
    }
  }
})
```

::tip{target="_blank" to="https://content.nuxt.com/docs/studio/config"}
This template integrates with Nuxt Studio, providing a visual interface for editing your documentation - perfect for non-technical contributors.
::
