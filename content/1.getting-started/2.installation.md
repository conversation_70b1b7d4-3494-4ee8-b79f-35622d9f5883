---
title: Installation
description: Get started with Nuxt UI Pro documentation template.
navigation:
  icon: i-lucide-download
---

::tip{target="_blank" to="https://content.nuxt.com/templates/docs"}
Use this template on Nuxt Studio and start your documentation in seconds.
::

## Quick Start

You can start a fresh new project with:

```bash [Terminal]
npx nuxi init -t github:nuxt-ui-pro/docs
```

or create a new repository from GitHub:

1. Open <https://github.com/nuxt-ui-pro/docs>
2. Click on `Use this template` button
3. Enter repository name and click on `Create repository from template` button
4. Clone your new repository
5. Install dependencies with your favorite package manager
6. Start development server

That's it! You can now start writing your documentation in the [`content/`](https://content.nuxt.com/usage/content-directory) directory 🚀
