---
title: Authentication
description: We provide a secure and straightforward authentication process to ensure the safety of your data and account.
---

## API keys

The GeminiGen.AI API uses API keys to authenticate requests. You can create and manage your API keys in the [Service Integration](https://dev.geminigen.ai/app/profile/integration/api-keys).

Remember that your API key is a secret, and you should never share it in publicly accessible areas such as GitHub, client-side code, and so on. If you believe your API key has been compromised, you can regenerate it at any time. Production requests must be routed through your own backend server where your API key can be securely loaded from an environment variable or key management service.

All API requests should include your API key in an `x-api-key` header. HTTP header as follows:

```
x-api-key: YOUR_API_KEY
```

::callout
---
icon: i-heroicons-light-bulb
---
Use can delete your API key at any time if you believe it has been compromised.
::
