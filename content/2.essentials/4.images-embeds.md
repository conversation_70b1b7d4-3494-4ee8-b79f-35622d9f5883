---
title: Images and Embeds
description: Add image, video, and other HTML elements
navigation:
  icon: i-lucide-image
---

## Image

### Using Markdown

Display images using standard Markdown syntax. Markdown images are simple to implement for basic image display.

::code-preview
![Nuxt Social Image](https://nuxt.com/new-social.jpg)

#code
```mdc
![Nuxt Social Image](https://nuxt.com/new-social.jpg)
```
::

::note
If [`@nuxt/image`](https://image.nuxt.com/get-started/installation) is installed, the `<NuxtImg>` component will be used instead of the native `img` tag.
::

### Using Embeds

Use embeds for more image customization. Embeds offer greater control over image attributes like size and styling.

::code-preview
![Nuxt Social Image](https://nuxt.com/new-social.jpg){height="150"}



#code
```mdc
:img{src="https://nuxt.com/new-social.jpg" alt="Nuxt Social Image" height="150"}
```
::

## Embeds and HTML elements

Embeds allow adding various HTML elements like videos and iframes. This feature extends documentation capabilities to include interactive and multimedia content.

::code-preview
---
class: "[&>div]:*:w-full"
---
:iframe{src="https://www.youtube-nocookie.com/embed/_eQxomah-nA?si=pDSzchUBDKb2NQu7" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen style="aspect-ratio: 16/9;"}


#code
```mdc
:iframe{src="https://www.youtube-nocookie.com/embed/_eQxomah-nA?si=pDSzchUBDKb2NQu7" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen style="aspect-ratio: 16/9;"}
```
::
