---
title: Prose Components
description: Components to help you structure your content.
navigation:
  icon: i-lucide-component
---

### Accordion

Use `accordion` and `accordion-item` to create collapsible content sections. Accordions are useful for organizing FAQs, expandable details, or grouped information in an interactive way.

::code-preview
---
class: "[&>div]:*:my-0"
---
  :::accordion
    ::::accordion-item
    ---
    icon: i-lucide-circle-help
    label: What are the main considerations when upgrading to Nuxt UI v3?
    ---
    The transition to v3 involves significant changes, including new component structures, updated theming approaches, and revised TypeScript definitions. We recommend a careful, incremental upgrade process, starting with thorough testing in a development environment.
    ::::
  
    ::::accordion-item
    ---
    icon: i-lucide-circle-help
    label: Is Nuxt UI v3 compatible with standalone Vue projects?
    ---
    Nuxt UI is now compatible with Vue! You can follow the [installation guide](/getting-started/installation) to get started.
    ::::
  
    ::::accordion-item{icon="i-lucide-circle-help" label="What about Nuxt UI Pro?"}
    We've also rebuilt Nuxt UI Pro from scratch and released a `v3.0.0-alpha.x` package but it only contains the components to build this documentation yet. This will be a free update, so the license you buy now will be valid for v3. We're actively working to finish the rewrite of all Nuxt UI Pro components.
    ::::
  :::

#code
```mdc
::accordion

:::accordion-item{label="What are the main considerations when upgrading to Nuxt UI v3?" icon="i-lucide-circle-help"}
The transition to v3 involves significant changes, including new component structures, updated theming approaches, and revised TypeScript definitions. We recommend a careful, incremental upgrade process, starting with thorough testing in a development environment.
:::

:::accordion-item{label="Is Nuxt UI v3 compatible with standalone Vue projects?" icon="i-lucide-circle-help"}
Nuxt UI is now compatible with Vue! You can follow the [installation guide](/getting-started/installation) to get started.
:::

:::accordion-item{label="What about Nuxt UI Pro?" icon="i-lucide-circle-help"}
We've also rebuilt Nuxt UI Pro from scratch and released a `v3.0.0-alpha.x` package but it only contains the components to build this documentation yet. This will be a free update, so the license you buy now will be valid for v3. We're actively working to finish the rewrite of all Nuxt UI Pro components.
:::

::
```
::

### Badge

Use badge to display status indicators or labels. Badges are great for highlighting version numbers, statuses, or categories within your content.

::code-preview
---
label: Preview
---
  :::badge
  **v3.0.0-alpha.10**
  :::

#code
```mdc
::badge
**v3.0.0-alpha.10**
::
```
::

### Callout

Use callout to emphasize important contextual information. Callouts draw attention to notes, tips, warnings, or cautions, making key information stand out.

Customize with `icon` and `color` props or use `note`, `tip`, `warning`, `caution` shortcuts for pre-defined semantic styles.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
  :::callout
  This is a `callout` with full **markdown** support.
  :::

#code
```mdc
::callout
This is a `callout` with full **markdown** support.
::
```
::

::code-preview
  :::div{.flex.flex-col.gap-4.w-full}
    ::::note{.w-full.my-0}
    Here's some additional information for you.
    ::::
  
    ::::tip{.w-full.my-0}
    Here's a helpful suggestion.
    ::::
  
    ::::warning{.w-full.my-0}
    Be careful with this action as it might have unexpected results.
    ::::
  
    ::::caution{.w-full.my-0}
    This action cannot be undone.
    ::::
  :::

#code
```mdc
::note
Here's some additional information.
::

::tip
Here's a helpful suggestion.
::

::warning
Be careful with this action as it might have unexpected results.
::

::caution
This action cannot be undone.
::
```
::

### Card

Use `card` to highlight content blocks. Cards are useful for showcasing features, resources, or related information in visually distinct and interactive containers.

Customize with `title`, `icon`, and `color` props. Cards can also act as links using `<NuxtLink>` properties for navigation.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
  :::card
  ---
  icon: i-simple-icons-github
  target: _blank
  title: Dashboard
  to: https://github.com/nuxt-ui-pro/dashboard
  ---
  A dashboard with multi-column layout.
  :::

#code
```mdc
::card
---
title: Dashboard
icon: i-simple-icons-github
to: https://github.com/nuxt-ui-pro/dashboard
target: _blank
---
A dashboard with multi-column layout.
::
```
::

### CardGroup

Use `card-group` to arrange cards in a grid layout. `card-group` is ideal for displaying collections of cards in a structured, visually appealing, and responsive grid.

::code-preview
  :::card-group{.w-full}
    ::::card
    ---
    icon: i-simple-icons-github
    target: _blank
    title: Dashboard
    to: https://github.com/nuxt-ui-pro/dashboard
    ---
    A dashboard with multi-column layout.
    ::::
  
    ::::card
    ---
    icon: i-simple-icons-github
    target: _blank
    title: SaaS
    to: https://github.com/nuxt-ui-pro/saas
    ---
    A template with landing, pricing, docs and blog.
    ::::
  
    ::::card
    ---
    icon: i-simple-icons-github
    target: _blank
    title: Docs
    to: https://github.com/nuxt-ui-pro/docs
    ---
    A documentation with `@nuxt/content`.
    ::::
  
    ::::card
    ---
    icon: i-simple-icons-github
    target: _blank
    title: Landing
    to: https://github.com/nuxt-ui-pro/landing
    ---
    A landing page you can use as starting point.
    ::::
  :::

#code
```mdc
::card-group

::card
---
title: Dashboard
icon: i-simple-icons-github
to: https://github.com/nuxt-ui-pro/dashboard
target: _blank
---
A dashboard with multi-column layout.
::

::card
---
title: SaaS
icon: i-simple-icons-github
to: https://github.com/nuxt-ui-pro/saas
target: _blank
---
A template with landing, pricing, docs and blog.
::

::card
---
title: Docs
icon: i-simple-icons-github
to: https://github.com/nuxt-ui-pro/docs
target: _blank
---
A documentation with `@nuxt/content`.
::

::card
---
title: Landing
icon: i-simple-icons-github
to: https://github.com/nuxt-ui-pro/landing
target: _blank
---
A landing page you can use as starting point.
::

::
```
::

### Collapsible

Use `collapsible` to hide and reveal content sections. `collapsible` is ideal for showing optional details, technical specifications, or less frequently needed information.

::code-preview
---
class: "[&>div]:*:w-full"
---
  :::collapsible
  | Prop    | Default   | Type     |
  | ------- | --------- | -------- |
  | `name`  |           | `string` |
  | `size`  | `md`      | `string` |
  | `color` | `neutral` | `string` |
  :::

#code
```mdc
::collapsible

| Prop    | Default   | Type                     |
|---------|-----------|--------------------------|
| `name`  |           | `string`{lang="ts-type"} |
| `size`  | `md`      | `string`{lang="ts-type"} |
| `color` | `neutral` | `string`{lang="ts-type"} |

::
```
::

### Field

Use `field` to describe a specific field, property, or parameter. `field` components are perfect for documenting API parameters, component props, or configuration options.

::code-preview
  :::field{.w-full required name="name" type="string"}
  The `description` can be set as prop or in the default slot with full **markdown** support.
  :::

#code
```mdc
::field{name="name" type="string" required}
The `description` can be set as prop or in the default slot with full **markdown** support.
::
```
::

### FieldGroup

Use `field-group` to group related fields together in a list. `field-group` helps organize and structure documentation for multiple related fields or properties.

::code-preview
  :::field-group
    ::::field{name="analytics" type="boolean"}
    Default to `false` - Enables analytics for your project (coming soon).
    ::::
  
    ::::field{name="blob" type="boolean"}
    Default to `false` - Enables blob storage to store static assets, such as images, videos and more.
    ::::
  
    ::::field{name="cache" type="boolean"}
    Default to `false` - Enables cache storage to cache your server route responses or functions using Nitro's `cachedEventHandler` and `cachedFunction`
    ::::
  
    ::::field{name="database" type="boolean"}
    Default to `false` - Enables SQL database to store your application's data.
    ::::
  :::

#code
```mdc
::field-group
  ::field{name="analytics" type="boolean"}
    Default to `false` - Enables analytics for your project (coming soon).
  ::

  ::field{name="blob" type="boolean"}
    Default to `false` - Enables blob storage to store static assets, such as images, videos and more.
  ::

  ::field{name="cache" type="boolean"}
    Default to `false` - Enables cache storage to cache your server route responses or functions using Nitro's `cachedEventHandler` and `cachedFunction`
  ::

  ::field{name="database" type="boolean"}
    Default to `false` - Enables SQL database to store your application's data.
  ::
::
```
::

### Icon

Use `icon` to insert icons from icon libraries. Icons provide visual cues and enhance the user interface of your documentation.

::code-preview
:icon{name="i-simple-icons-nuxtdotjs"}

 

#code
```mdc
:icon{name="i-simple-icons-nuxtdotjs"}
```
::

### Kbd

Use `kbd` to display keyboard keys or shortcuts. `kbd` components clearly indicate keyboard inputs for instructions or command references.

::code-preview
---
class: "[&>div]:*:my-0"
---
`` ``

#code
```mdc
:kbd{value="meta"} :kbd{value="K"}
```
::

### Tabs

Use `tabs` and `tabs-item` to organize content into tabbed interfaces. Tabs are effective for separating content into logical sections, improving content discoverability and organization.

::code-preview
---
class: "[&>div]:*:my-0"
---
  :::tabs{.w-full}
    ::::tabs-item{icon="i-lucide-code" label="Code"}
    ```mdc
    ::callout
    Lorem velit voluptate ex reprehenderit ullamco et culpa.
    ::
    ```
    ::::
  
    ::::tabs-item{icon="i-lucide-eye" label="Preview"}
      :::::callout
      Lorem velit voluptate ex reprehenderit ullamco et culpa.
      :::::
    ::::
  :::

#code
````mdc
::tabs

:::tabs-item{label="Code" icon="i-lucide-code"}

```mdc
::callout
Lorem velit voluptate ex reprehenderit ullamco et culpa.
::
```

:::

:::tabs-item{label="Preview" icon="i-lucide-eye"}

::::callout
Lorem velit voluptate ex reprehenderit ullamco et culpa.
::::

:::

::
````
::

### Steps

Use `steps` to create step-by-step guides from document headings. `steps` component automatically numbers headings, creating a numbered guide for processes and tutorials.

Set the `level` prop to define the heading level to include in the step-by-step guide.

::code-preview
---
class: "[&>div]:*:w-full"
---
  :::steps{level="4"}
  #### Add the Nuxt UI Pro module in your `nuxt.config.ts`
  
  ```ts [nuxt.config.ts]
  export default defineNuxtConfig({
    modules: ['@nuxt/ui-pro']
  })
  ```
  
  #### Import Tailwind CSS and Nuxt UI Pro in your CSS
  
  ```css [assets/css/main.css]
  @import "tailwindcss";
  @import "@nuxt/ui-pro";
  ```
  :::

#code
````mdc
::steps{level="4"}

#### Add the Nuxt UI Pro module in your `nuxt.config.ts`{lang="ts-type"}

```ts [nuxt.config.ts]
export default defineNuxtConfig({
  modules: ['@nuxt/ui-pro']
})
```

#### Import Tailwind CSS and Nuxt UI Pro in your CSS

```css [assets/css/main.css]
@import "tailwindcss";
@import "@nuxt/ui-pro";
```

::
````
::
