---
title: Code Blocks
description: Display inline code and code blocks
navigation:
  icon: i-lucide-code-xml
---

## Basic

### Inline Code

Use inline code to display code snippets within text paragraphs. It's ideal for referencing code elements directly in sentences.

::code-preview
---
class: "[&>div]:*:my-0"
---
`inline code`

#code
```mdc
`inline code`
```
::

### Code Blocks

Use code blocks to display multi-line code snippets with syntax highlighting. Code blocks are essential for presenting code examples clearly.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
```ts
export default defineNuxtConfig({
  modules: ['@nuxt/ui-pro']
})
```

#code
````mdc
```ts
export default defineNuxtConfig({
  modules: ['@nuxt/ui-pro']
})
```
````
::

When writing a code-block, you can specify a filename that will be displayed on top of the code block. An icon will be automatically displayed based on the extension or the name.
Filenames help users understand the code's location and purpose within a project.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
```ts [nuxt.config.ts]
export default defineNuxtConfig({
  modules: ['@nuxt/ui-pro']
})
```

#code
````mdc
```ts [nuxt.config.ts]
export default defineNuxtConfig({
  modules: ['@nuxt/ui-pro']
})
```
````
::

::tip
Some icons are already defined by default, but you can add more in your `app.config.ts` through the `uiPro.prose.codeIcon` key:

```ts [app.config.ts]
export default defineAppConfig({
  uiPro: {
    prose: {
      codeIcon: {
        terminal: 'i-ph-terminal-window-duotone'
      }
    }
  }
})
```
::

Every code-block has a built-in copy button that will copy the code to your clipboard.

::tip
You can change the icon in your `app.config.ts` through the `ui.icons.copy` and `ui.icons.copyCheck` keys:

```ts [app.config.ts]
export default defineAppConfig({
  ui: {
    icons: {
      copy: 'i-lucide-copy',
      copyCheck: 'i-lucide-copy-check'
    }
  }
})
```
::

#### Highlight Line

To highlight lines of code, add `{}` around the line numbers you want to highlight.
Line highlighting is useful for focusing users on important parts of code examples.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
```ts [nuxt.config.ts]
export default defineAppConfig({
  ui: {
    icons: {
      copy: 'i-lucide-copy',
      copyCheck: 'i-lucide-copy-check'
    }
  }
})
```

#code
````mdc
```ts [nuxt.config.ts]{4-5}
export default defineAppConfig({
  ui: {
    icons: {
      copy: 'i-lucide-copy',
      copyCheck: 'i-lucide-copy-check'
    }
  }
})
```
````
::

## Advanced

### CodeGroup

Group code blocks in tabs using `code-group`. `code-group` is perfect for showing code examples in multiple languages or package managers.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
  :::code-group{.w-full}
  ```bash [pnpm]
  pnpm add @nuxt/ui-pro@next
  ```
  
  ```bash [yarn]
  yarn add @nuxt/ui-pro@next
  ```
  
  ```bash [npm]
  npm install @nuxt/ui-pro@next
  ```
  
  ```bash [bun]
  bun add @nuxt/ui-pro@next
  ```
  :::

#code
````mdc
:::code-group

```bash [pnpm]
pnpm add @nuxt/ui-pro@next
```

```bash [yarn]
yarn add @nuxt/ui-pro@next
```

```bash [npm]
npm install @nuxt/ui-pro@next
```

```bash [bun]
bun add @nuxt/ui-pro@next
```

::
````
::

::note{to="#pre"}
Like the `ProsePre` component, the `CodeGroup` handles filenames, icons and copy button.
::

### CodeTree

Display code blocks in a file tree view using `code-tree`. `code-tree` is excellent for showcasing project structures and file relationships.

::code-preview{class="[&>div]:*:my-0 [&>div]:*:w-full"}
  :::code-tree{default-value="app/app.config.ts"}
  ```ts [nuxt.config.ts]
  export default defineNuxtConfig({
    modules: ['@nuxt/ui-pro'],
  
    future: {
      compatibilityVersion: 4
    },
  
    css: ['~/assets/css/main.css']
  })
  
  ```
  
  ```css [app/assets/css/main.css]
  @import "tailwindcss";
  @import "@nuxt/ui-pro";
  ```
  
  ```ts [app/app.config.ts]
  export default defineAppConfig({
    ui: {
      colors: {
        primary: 'sky',
        colors: 'slate'
      }
    }
  })
  ```
  
  ```vue [app/app.vue]
  <template>
    <UApp>
      <NuxtPage />
    </UApp>
  </template>
  ```
  
  ```json [package.json]
  {
    "name": "nuxt-app",
    "private": true,
    "type": "module",
    "scripts": {
      "build": "nuxt build",
      "dev": "nuxt dev",
      "generate": "nuxt generate",
      "preview": "nuxt preview",
      "postinstall": "nuxt prepare",
      "lint": "eslint .",
      "lint:fix": "eslint --fix ."
    },
    "dependencies": {
      "@iconify-json/lucide": "^1.2.18",
      "@nuxt/ui-pro": "3.0.0-alpha.10",
      "nuxt": "^3.15.1"
    },
    "devDependencies": {
      "eslint": "9.20.1",
      "typescript": "^5.7.2",
      "vue-tsc": "^2.2.0"
    }
  }
  ```
  
  ```json [tsconfig.json]
  {
    "extends": "./.nuxt/tsconfig.json"
  }
  ```
  
  ````md [README.md]
  # Nuxt 3 Minimal Starter
  
  Look at the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.
  
  ## Setup
  
  Make sure to install the dependencies:
  
  ```bash
  # npm
  npm install
  
  # pnpm
  pnpm install
  
  # yarn
  yarn install
  
  # bun
  bun install
  ```
  
  ## Development Server
  
  Start the development server on `http://localhost:3000`:
  
  ```bash
  # npm
  npm run dev
  
  # pnpm
  pnpm run dev
  
  # yarn
  yarn dev
  
  # bun
  bun run dev
  ```
  
  ## Production
  
  Build the application for production:
  
  ```bash
  # npm
  npm run build
  
  # pnpm
  pnpm run build
  
  # yarn
  yarn build
  
  # bun
  bun run build
  ```
  
  Locally preview production build:
  
  ```bash
  # npm
  npm run preview
  
  # pnpm
  pnpm run preview
  
  # yarn
  yarn preview
  
  # bun
  bun run preview
  ```
  
  Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.
  ````
  :::
::

::note
---
target: _blank
to: https://ui.nuxt.com/getting-started/typography#codetree
---
Learn more about the `code-tree` component.
::

### `CodePreview`

Use `code-preview` to show code output alongside the code. `code-preview` is ideal for interactive examples and demonstrating code results.
Write the code to be previewed in a the `default` slot and the actual code in the `code` slot.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
label: Preview
---
  :::code-preview
  ---
  class: "[&>div]:*:my-0"
  ---
  `inline code`
  
  #code
  ```mdc
  `inline code`
  ```
  :::

#code
````mdc
::code-preview
`inline code`

#code
```mdc
`inline code`
```
::
````
::

### `CodeCollapse`

Use `code-collapse` for long code blocks to keep pages clean. `code-collapse` allows users to expand code blocks only when needed, improving readability.

::code-preview
---
class: "[&>div]:*:my-0 [&>div]:*:w-full"
---
  :::code-collapse{class="[&>div]:my-0"}
  ```css [main.css]
  @import "tailwindcss";
  @import "@nuxt/ui-pro";
  
  @theme {
    --font-sans: 'Public Sans', sans-serif;
  
    --breakpoint-3xl: 1920px;
  
    --color-green-50: #EFFDF5;
    --color-green-100: #D9FBE8;
    --color-green-200: #B3F5D1;
    --color-green-300: #75EDAE;
    --color-green-400: #00DC82;
    --color-green-500: #00C16A;
    --color-green-600: #00A155;
    --color-green-700: #007F45;
    --color-green-800: #016538;
    --color-green-900: #0A5331;
    --color-green-950: #052E16;
  }
  ```
  :::

#code
````mdc
::code-collapse

```css [main.css]
@import "tailwindcss";
@import "@nuxt/ui-pro";

@theme {
  --font-sans: 'Public Sans', sans-serif;

  --breakpoint-3xl: 1920px;

  --color-green-50: #EFFDF5;
  --color-green-100: #D9FBE8;
  --color-green-200: #B3F5D1;
  --color-green-300: #75EDAE;
  --color-green-400: #00DC82;
  --color-green-500: #00C16A;
  --color-green-600: #00A155;
  --color-green-700: #007F45;
  --color-green-800: #016538;
  --color-green-900: #0A5331;
  --color-green-950: #052E16;
}
```

::
````
::
