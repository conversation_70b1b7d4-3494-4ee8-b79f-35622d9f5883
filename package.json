{"name": "nuxt-ui-pro-template-docs", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.57", "@iconify-json/simple-icons": "^1.2.43", "@iconify-json/vscode-icons": "^1.2.23", "@nuxt/content": "^3.6.3", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "^3.2.0", "better-sqlite3": "^12.2.0", "nuxt": "^4.0.0", "nuxt-llms": "0.1.3", "nuxt-og-image": "^5.1.9"}, "devDependencies": {"@nuxt/eslint": "^1.5.2", "eslint": "^9.31.0", "typescript": "^5.8.3", "vue-tsc": "^3.0.1"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"onlyBuiltDependencies": ["better-sqlite3"], "ignoredBuiltDependencies": ["vue-demi"]}, "packageManager": "pnpm@10.13.1"}